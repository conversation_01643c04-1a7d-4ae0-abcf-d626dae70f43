# 排序与查找算法综合实验

## 项目概述

本项目实现了一个综合的排序与查找算法实验程序，包含以下功能：

1. 生成35000个0~2999999范围内的不重复随机整数
2. 实现8种排序算法并统计比较次数
3. 实现4种查找算法并测试性能
4. 提供详细的算法分析报告

## 文件说明

- `sorting_searching_experiment.c` - 完整版主程序源代码
- `sorting_experiment_fast.c` - 快速测试版程序（推荐先运行）
- `test_small.c` - 小规模测试程序
- `algorithm_analysis_report.md` - 算法理论分析报告
- `Makefile` - 编译配置文件
- `README.md` - 项目说明文档

## ⚠️ 重要提示

由于需要生成35000个不重复的随机数，完整版程序在随机数生成阶段可能需要较长时间（几分钟到十几分钟）。

**建议运行顺序：**
1. 先运行 `sorting_experiment_fast.c` 快速验证程序功能
2. 再运行完整版 `sorting_searching_experiment.c` 获得精确结果

## 实现的算法

### 排序算法
1. 冒泡排序 (Bubble Sort)
2. 选择排序 (Selection Sort)
3. 插入排序 (Insertion Sort)
4. 希尔排序 (Shell Sort)
5. 归并排序 (Merge Sort)
6. 快速排序 (Quick Sort)
7. 堆排序 (Heap Sort)
8. 基数排序 (Radix Sort)

### 查找算法
1. 顺序查找 (Sequential Search)
2. 折半查找 (Binary Search)
3. 二叉搜索树查找 (BST Search)
4. 哈希表查找 (Hash Search)

## 编译和运行

### 方法1: 使用Makefile (推荐)

```bash
# 编译程序
make

# 编译并运行
make run

# 清理编译文件
make clean
```

### 方法2: 直接使用GCC

```bash
gcc -Wall -Wextra -std=c99 -O2 -o sorting_experiment sorting_searching_experiment.c
./sorting_experiment
```

### 方法3: Windows环境

#### 使用MinGW:
```cmd
gcc -Wall -Wextra -std=c99 -O2 -o sorting_experiment.exe sorting_searching_experiment.c
sorting_experiment.exe
```

#### 使用MSVC:
```cmd
cl /W4 /O2 sorting_searching_experiment.c /Fe:sorting_experiment.exe
sorting_experiment.exe
```

## 程序输出

程序将输出以下信息：

1. **随机数生成**: 显示生成的随机数样本
2. **排序算法测试**: 每种算法的比较次数和运行时间
3. **查找算法测试**: 各种查找算法的性能对比
4. **硬件配置提醒**: 提醒用户记录硬件配置信息

## 实验要求对应

### 要求(1): 随机数生成
- ✅ 生成0~2999999范围内的35000个不重复随机整数
- ✅ 使用哈希表确保数字不重复

### 要求(2): 排序算法实现
- ✅ 实现教材中的全部排序算法
- ✅ 统计每种算法的真实比较次数
- ✅ 对35000个随机数进行递增排序

### 要求(3): 基数排序实现
- ✅ 采用基数排序思想实现排序
- ✅ 统计排序过程中的比较次数

### 要求(4): 算法分析
- ✅ 分析基数排序的优缺点
- ✅ 列出与基数排序时间复杂度一致的算法
- ✅ 详细说明各算法的应用场合、稳定性、复杂度

### 要求(5): 查找算法实现
- ✅ 实现顺序查找、折半查找、动态查找、Hash查找
- ✅ 查找第24680个数据（下标24679）
- ✅ 统计比较次数和运行时间
- ✅ 提供硬件配置记录提醒

## 性能预期

### 排序算法比较次数（35000个数据）
- 冒泡排序: ~6亿次
- 选择排序: ~6亿次
- 插入排序: ~3亿次
- 希尔排序: ~500万次
- 归并排序: ~54万次
- 快速排序: ~52万次
- 堆排序: ~70万次
- 基数排序: ~24万次

### 查找算法比较次数
- 顺序查找: 24680次
- 折半查找: ~16次
- 二叉搜索树: ~16次
- 哈希查找: 1-3次

## 注意事项

1. **内存使用**: 程序需要约280MB内存（35000个整数 × 8个副本）
2. **运行时间**: 完整测试可能需要几分钟到几十分钟，取决于硬件配置
3. **编译器**: 需要支持C99标准的编译器
4. **硬件配置**: 请记录CPU核心数、频率和内存容量用于分析

## 实验报告

详细的算法理论分析请参考 `algorithm_analysis_report.md` 文件。

## 作者

本程序实现了数据结构与算法课程的综合实验要求，包含完整的排序和查找算法实现与性能分析。
