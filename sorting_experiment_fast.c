#include <stdio.h>
#include <stdlib.h>
#include <time.h>
#include <string.h>
#include <stdbool.h>

#define DATA_SIZE 35000
#define MAX_VALUE 2999999
#define TARGET_INDEX 24679

long long comparison_count = 0;

// 快速随机数生成 - 使用线性同余生成器避免重复检查
void generate_unique_random_numbers_fast(int *arr, int size, int max_val) {
    printf("正在快速生成 %d 个随机数...\n", size);
    
    srand(time(NULL));
    
    // 使用简单的方法：生成足够分散的数字
    // 由于范围很大(3000000)而需要的数字相对较少(35000)，冲突概率很低
    
    for (int i = 0; i < size; i++) {
        // 使用多个随机数源来减少冲突
        int base = (rand() % (max_val / size)) * size;  // 基础偏移
        int offset = rand() % (max_val / size + 1);     // 随机偏移
        arr[i] = (base + offset + i * 73) % (max_val + 1);  // 添加线性分布
        
        // 简单的冲突检测和解决（只检查最近的几个数字）
        for (int j = i - 1; j >= 0 && j >= i - 100; j--) {
            if (arr[i] == arr[j]) {
                arr[i] = (arr[i] + 1009) % (max_val + 1);  // 使用质数偏移
                break;
            }
        }
        
        if ((i + 1) % 5000 == 0) {
            printf("已生成 %d/%d 个随机数\n", i + 1, size);
        }
    }
    
    printf("随机数生成完成！\n");
}

void copy_array(int *src, int *dest, int size) {
    for (int i = 0; i < size; i++) {
        dest[i] = src[i];
    }
}

void print_array_sample(int *arr, int size, const char *title) {
    printf("\n%s (前20个和后20个):\n", title);
    printf("前20个: ");
    for (int i = 0; i < 20 && i < size; i++) {
        printf("%d ", arr[i]);
    }
    printf("\n后20个: ");
    for (int i = size - 20; i < size; i++) {
        printf("%d ", arr[i]);
    }
    printf("\n");
}

// 快速排序实现
int partition(int *arr, int low, int high) {
    int pivot = arr[high];
    int i = (low - 1);
    
    for (int j = low; j <= high - 1; j++) {
        comparison_count++;
        if (arr[j] < pivot) {
            i++;
            int temp = arr[i];
            arr[i] = arr[j];
            arr[j] = temp;
        }
    }
    int temp = arr[i + 1];
    arr[i + 1] = arr[high];
    arr[high] = temp;
    return (i + 1);
}

void quick_sort_helper(int *arr, int low, int high) {
    if (low < high) {
        int pi = partition(arr, low, high);
        quick_sort_helper(arr, low, pi - 1);
        quick_sort_helper(arr, pi + 1, high);
    }
}

void quick_sort(int *arr, int size) {
    comparison_count = 0;
    quick_sort_helper(arr, 0, size - 1);
}

// 归并排序实现
void merge(int *arr, int left, int mid, int right) {
    int n1 = mid - left + 1;
    int n2 = right - mid;
    
    int *L = (int*)malloc(n1 * sizeof(int));
    int *R = (int*)malloc(n2 * sizeof(int));
    
    for (int i = 0; i < n1; i++)
        L[i] = arr[left + i];
    for (int j = 0; j < n2; j++)
        R[j] = arr[mid + 1 + j];
    
    int i = 0, j = 0, k = left;
    
    while (i < n1 && j < n2) {
        comparison_count++;
        if (L[i] <= R[j]) {
            arr[k] = L[i];
            i++;
        } else {
            arr[k] = R[j];
            j++;
        }
        k++;
    }
    
    while (i < n1) {
        arr[k] = L[i];
        i++;
        k++;
    }
    
    while (j < n2) {
        arr[k] = R[j];
        j++;
        k++;
    }
    
    free(L);
    free(R);
}

void merge_sort_helper(int *arr, int left, int right) {
    if (left < right) {
        int mid = left + (right - left) / 2;
        merge_sort_helper(arr, left, mid);
        merge_sort_helper(arr, mid + 1, right);
        merge(arr, left, mid, right);
    }
}

void merge_sort(int *arr, int size) {
    comparison_count = 0;
    merge_sort_helper(arr, 0, size - 1);
}

// 基数排序实现
int get_max(int *arr, int size) {
    int max = arr[0];
    for (int i = 1; i < size; i++) {
        if (arr[i] > max)
            max = arr[i];
    }
    return max;
}

void counting_sort_for_radix(int *arr, int size, int exp) {
    int *output = (int*)malloc(size * sizeof(int));
    int count[10] = {0};
    
    for (int i = 0; i < size; i++)
        count[(arr[i] / exp) % 10]++;
    
    for (int i = 1; i < 10; i++)
        count[i] += count[i - 1];
    
    for (int i = size - 1; i >= 0; i--) {
        output[count[(arr[i] / exp) % 10] - 1] = arr[i];
        count[(arr[i] / exp) % 10]--;
    }
    
    for (int i = 0; i < size; i++)
        arr[i] = output[i];
    
    free(output);
}

void radix_sort(int *arr, int size) {
    comparison_count = 0;
    int max = get_max(arr, size);
    
    for (int exp = 1; max / exp > 0; exp *= 10) {
        counting_sort_for_radix(arr, size, exp);
    }
    comparison_count = size - 1; // 基数排序的比较主要在找最大值
}

// 二分查找
int binary_search(int *arr, int size, int key, long long *comparisons) {
    *comparisons = 0;
    int left = 0, right = size - 1;
    
    while (left <= right) {
        int mid = left + (right - left) / 2;
        (*comparisons)++;
        
        if (arr[mid] == key) {
            return mid;
        } else if (arr[mid] < key) {
            (*comparisons)++;
            left = mid + 1;
        } else {
            (*comparisons)++;
            right = mid - 1;
        }
    }
    return -1;
}

void test_sorting_algorithm(const char *name, void (*sort_func)(int*, int), int *original_data) {
    int *data = (int*)malloc(DATA_SIZE * sizeof(int));
    copy_array(original_data, data, DATA_SIZE);
    
    printf("\n=== %s ===\n", name);
    clock_t start = clock();
    sort_func(data, DATA_SIZE);
    clock_t end = clock();
    
    double time_taken = ((double)(end - start)) / CLOCKS_PER_SEC;
    printf("比较次数: %lld\n", comparison_count);
    printf("运行时间: %.6f 秒\n", time_taken);
    
    // 验证排序
    bool is_sorted = true;
    for (int i = 0; i < DATA_SIZE - 1; i++) {
        if (data[i] > data[i + 1]) {
            is_sorted = false;
            break;
        }
    }
    printf("排序结果: %s\n", is_sorted ? "正确" : "错误");
    
    if (strcmp(name, "归并排序") == 0) {
        copy_array(data, original_data, DATA_SIZE);
    }
    
    free(data);
}

int main() {
    printf("=== 排序与查找算法快速测试版 ===\n");
    printf("数据规模: %d\n", DATA_SIZE);
    printf("数据范围: 0 ~ %d\n", MAX_VALUE);
    
    int *original_data = (int*)malloc(DATA_SIZE * sizeof(int));
    
    // 快速生成随机数
    generate_unique_random_numbers_fast(original_data, DATA_SIZE, MAX_VALUE);
    
    print_array_sample(original_data, DATA_SIZE, "原始随机数据");
    
    // 测试主要的排序算法
    printf("\n=== 排序算法性能测试 ===\n");
    test_sorting_algorithm("快速排序", quick_sort, original_data);
    test_sorting_algorithm("归并排序", merge_sort, original_data);
    test_sorting_algorithm("基数排序", radix_sort, original_data);
    
    // 测试查找
    int *sorted_data = (int*)malloc(DATA_SIZE * sizeof(int));
    copy_array(original_data, sorted_data, DATA_SIZE);
    merge_sort(sorted_data, DATA_SIZE);
    
    print_array_sample(sorted_data, DATA_SIZE, "排序后数据");
    
    printf("\n=== 查找算法测试 ===\n");
    int target_value = sorted_data[TARGET_INDEX];
    long long comparisons;
    
    printf("目标值: %d (位置: %d)\n", target_value, TARGET_INDEX);
    
    clock_t start = clock();
    int result = binary_search(sorted_data, DATA_SIZE, target_value, &comparisons);
    clock_t end = clock();
    double time_taken = ((double)(end - start)) / CLOCKS_PER_SEC;
    
    printf("二分查找结果: 位置 %d, 比较次数: %lld, 时间: %.6f秒\n", 
           result, comparisons, time_taken);
    
    printf("\n=== 测试完成 ===\n");
    printf("注意: 这是快速版本，随机数可能有少量重复，但足以测试算法性能\n");
    
    free(original_data);
    free(sorted_data);
    
    return 0;
}
