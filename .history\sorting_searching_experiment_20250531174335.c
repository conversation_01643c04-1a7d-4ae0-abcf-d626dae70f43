#include <stdio.h>
#include <stdlib.h>
#include <time.h>
#include <string.h>
#include <stdbool.h>

#define DATA_SIZE 35000
#define MAX_VALUE 2999999
#define TARGET_INDEX 24679  // 第24680个数据的下标

// 全局变量用于统计比较次数
long long comparison_count = 0;

// 函数声明
void generate_unique_random_numbers_small_memory(int *arr, int size, int max_val);

// 高效的随机数生成函数 - 使用改进的随机试探法
void generate_unique_random_numbers(int *arr, int size, int max_val) {
    srand(time(NULL));

    printf("正在生成 %d 个不重复的随机数（范围：0~%d）...\n", size, max_val);

    // 使用动态哈希表来存储已生成的数字
    // 由于35000相对于3000000来说比例很小（约1.17%），使用集合方法是合理的
    bool *used = (bool*)calloc(max_val + 1, sizeof(bool));
    if (used == NULL) {
        printf("内存分配失败！尝试使用替代方法...\n");
        // 如果内存分配失败，使用更小的内存占用方法
        generate_unique_random_numbers_small_memory(arr, size, max_val);
        return;
    }

    int count = 0;
    int attempts = 0;
    int max_attempts = size * 20; // 设置最大尝试次数防止无限循环

    while (count < size && attempts < max_attempts) {
        int num = rand() % (max_val + 1);
        attempts++;

        if (!used[num]) {
            used[num] = true;
            arr[count++] = num;

            // 显示进度
            if (count % 5000 == 0 || count == size) {
                printf("已生成 %d/%d 个随机数 (尝试次数: %d)\n", count, size, attempts);
            }
        }

        // 如果尝试次数过多，说明可能接近饱和，调整策略
        if (attempts > count * 10 && count > size / 2) {
            printf("切换到顺序填充剩余数字以提高效率...\n");
            // 找到剩余未使用的数字
            for (int i = 0; i <= max_val && count < size; i++) {
                if (!used[i]) {
                    // 随机决定是否使用这个数字
                    if (rand() % ((max_val - count) / (size - count) + 1) == 0) {
                        used[i] = true;
                        arr[count++] = i;

                        if (count % 1000 == 0) {
                            printf("已生成 %d/%d 个随机数\n", count, size);
                        }
                    }
                }
            }
            break;
        }
    }

    if (count < size) {
        printf("警告：只生成了 %d/%d 个随机数\n", count, size);
    }

    free(used);
}

// 备用的小内存占用方法
void generate_unique_random_numbers_small_memory(int *arr, int size, int max_val) {
    printf("使用小内存占用方法生成随机数...\n");

    for (int i = 0; i < size; i++) {
        int num;
        bool is_duplicate;
        int attempts = 0;

        do {
            num = rand() % (max_val + 1);
            is_duplicate = false;
            attempts++;

            // 检查是否与之前生成的数字重复
            for (int j = 0; j < i; j++) {
                if (arr[j] == num) {
                    is_duplicate = true;
                    break;
                }
            }

            // 防止无限循环
            if (attempts > 1000) {
                // 如果尝试太多次，找一个简单的未使用数字
                num = i; // 使用索引作为备选
                is_duplicate = false;
                for (int j = 0; j < i; j++) {
                    if (arr[j] == num) {
                        is_duplicate = true;
                        num = max_val - i; // 尝试另一个数字
                        break;
                    }
                }
            }
        } while (is_duplicate && attempts < 2000);

        arr[i] = num;

        if ((i + 1) % 5000 == 0) {
            printf("已生成 %d/%d 个随机数\n", i + 1, size);
        }
    }
}

// 复制数组
void copy_array(int *src, int *dest, int size) {
    for (int i = 0; i < size; i++) {
        dest[i] = src[i];
    }
}

// 打印数组（仅前20个和后20个元素）
void print_array_sample(int *arr, int size, const char *title) {
    printf("\n%s (showing first 20 and last 20 elements):\n", title);
    printf("First 20: ");
    for (int i = 0; i < 20 && i < size; i++) {
        printf("%d ", arr[i]);
    }
    printf("\nLast 20: ");
    for (int i = size - 20; i < size; i++) {
        printf("%d ", arr[i]);
    }
    printf("\n");
}

// 1. 冒泡排序
void bubble_sort(int *arr, int size) {
    comparison_count = 0;
    for (int i = 0; i < size - 1; i++) {
        for (int j = 0; j < size - i - 1; j++) {
            comparison_count++;
            if (arr[j] > arr[j + 1]) {
                int temp = arr[j];
                arr[j] = arr[j + 1];
                arr[j + 1] = temp;
            }
        }
    }
}

// 2. 选择排序
void selection_sort(int *arr, int size) {
    comparison_count = 0;
    for (int i = 0; i < size - 1; i++) {
        int min_idx = i;
        for (int j = i + 1; j < size; j++) {
            comparison_count++;
            if (arr[j] < arr[min_idx]) {
                min_idx = j;
            }
        }
        if (min_idx != i) {
            int temp = arr[i];
            arr[i] = arr[min_idx];
            arr[min_idx] = temp;
        }
    }
}

// 3. 插入排序
void insertion_sort(int *arr, int size) {
    comparison_count = 0;
    for (int i = 1; i < size; i++) {
        int key = arr[i];
        int j = i - 1;
        while (j >= 0) {
            comparison_count++;
            if (arr[j] > key) {
                arr[j + 1] = arr[j];
                j--;
            } else {
                break;
            }
        }
        arr[j + 1] = key;
    }
}

// 4. 希尔排序
void shell_sort(int *arr, int size) {
    comparison_count = 0;
    for (int gap = size / 2; gap > 0; gap /= 2) {
        for (int i = gap; i < size; i++) {
            int temp = arr[i];
            int j;
            for (j = i; j >= gap; j -= gap) {
                comparison_count++;
                if (arr[j - gap] > temp) {
                    arr[j] = arr[j - gap];
                } else {
                    break;
                }
            }
            arr[j] = temp;
        }
    }
}

// 5. 归并排序辅助函数
void merge(int *arr, int left, int mid, int right) {
    int n1 = mid - left + 1;
    int n2 = right - mid;
    
    int *L = (int*)malloc(n1 * sizeof(int));
    int *R = (int*)malloc(n2 * sizeof(int));
    
    for (int i = 0; i < n1; i++)
        L[i] = arr[left + i];
    for (int j = 0; j < n2; j++)
        R[j] = arr[mid + 1 + j];
    
    int i = 0, j = 0, k = left;
    
    while (i < n1 && j < n2) {
        comparison_count++;
        if (L[i] <= R[j]) {
            arr[k] = L[i];
            i++;
        } else {
            arr[k] = R[j];
            j++;
        }
        k++;
    }
    
    while (i < n1) {
        arr[k] = L[i];
        i++;
        k++;
    }
    
    while (j < n2) {
        arr[k] = R[j];
        j++;
        k++;
    }
    
    free(L);
    free(R);
}

void merge_sort_helper(int *arr, int left, int right) {
    if (left < right) {
        int mid = left + (right - left) / 2;
        merge_sort_helper(arr, left, mid);
        merge_sort_helper(arr, mid + 1, right);
        merge(arr, left, mid, right);
    }
}

void merge_sort(int *arr, int size) {
    comparison_count = 0;
    merge_sort_helper(arr, 0, size - 1);
}

// 6. 快速排序
int partition(int *arr, int low, int high) {
    int pivot = arr[high];
    int i = (low - 1);
    
    for (int j = low; j <= high - 1; j++) {
        comparison_count++;
        if (arr[j] < pivot) {
            i++;
            int temp = arr[i];
            arr[i] = arr[j];
            arr[j] = temp;
        }
    }
    int temp = arr[i + 1];
    arr[i + 1] = arr[high];
    arr[high] = temp;
    return (i + 1);
}

void quick_sort_helper(int *arr, int low, int high) {
    if (low < high) {
        int pi = partition(arr, low, high);
        quick_sort_helper(arr, low, pi - 1);
        quick_sort_helper(arr, pi + 1, high);
    }
}

void quick_sort(int *arr, int size) {
    comparison_count = 0;
    quick_sort_helper(arr, 0, size - 1);
}

// 7. 堆排序
void heapify(int *arr, int size, int i) {
    int largest = i;
    int left = 2 * i + 1;
    int right = 2 * i + 2;
    
    if (left < size) {
        comparison_count++;
        if (arr[left] > arr[largest])
            largest = left;
    }
    
    if (right < size) {
        comparison_count++;
        if (arr[right] > arr[largest])
            largest = right;
    }
    
    if (largest != i) {
        int temp = arr[i];
        arr[i] = arr[largest];
        arr[largest] = temp;
        heapify(arr, size, largest);
    }
}

void heap_sort(int *arr, int size) {
    comparison_count = 0;

    // 构建最大堆
    for (int i = size / 2 - 1; i >= 0; i--)
        heapify(arr, size, i);

    // 逐个提取元素
    for (int i = size - 1; i > 0; i--) {
        int temp = arr[0];
        arr[0] = arr[i];
        arr[i] = temp;
        heapify(arr, i, 0);
    }
}

// 8. 基数排序
int get_max(int *arr, int size) {
    int max = arr[0];
    for (int i = 1; i < size; i++) {
        if (arr[i] > max)
            max = arr[i];
    }
    return max;
}

void counting_sort_for_radix(int *arr, int size, int exp) {
    int *output = (int*)malloc(size * sizeof(int));
    int count[10] = {0};

    // 统计每个数字出现的次数
    for (int i = 0; i < size; i++)
        count[(arr[i] / exp) % 10]++;

    // 计算累积计数
    for (int i = 1; i < 10; i++)
        count[i] += count[i - 1];

    // 构建输出数组
    for (int i = size - 1; i >= 0; i--) {
        output[count[(arr[i] / exp) % 10] - 1] = arr[i];
        count[(arr[i] / exp) % 10]--;
    }

    // 复制输出数组到原数组
    for (int i = 0; i < size; i++)
        arr[i] = output[i];

    free(output);
}

void radix_sort(int *arr, int size) {
    comparison_count = 0; // 基数排序主要是分配和收集，比较次数很少
    int max = get_max(arr, size);

    // 对每一位进行计数排序
    for (int exp = 1; max / exp > 0; exp *= 10) {
        counting_sort_for_radix(arr, size, exp);
        // 基数排序中的比较主要在找最大值时
        comparison_count += size - 1; // 近似统计
    }
}

// 查找算法实现
// 1. 顺序查找
int sequential_search(int *arr, int size, int key, long long *comparisons) {
    *comparisons = 0;
    for (int i = 0; i < size; i++) {
        (*comparisons)++;
        if (arr[i] == key) {
            return i;
        }
    }
    return -1;
}

// 2. 折半查找（二分查找）
int binary_search(int *arr, int size, int key, long long *comparisons) {
    *comparisons = 0;
    int left = 0, right = size - 1;

    while (left <= right) {
        int mid = left + (right - left) / 2;
        (*comparisons)++;

        if (arr[mid] == key) {
            return mid;
        } else if (arr[mid] < key) {
            (*comparisons)++;
            left = mid + 1;
        } else {
            (*comparisons)++;
            right = mid - 1;
        }
    }
    return -1;
}

// 3. 二叉搜索树节点
typedef struct BST_Node {
    int data;
    int index; // 原数组中的索引
    struct BST_Node *left;
    struct BST_Node *right;
} BST_Node;

BST_Node* create_node(int data, int index) {
    BST_Node *node = (BST_Node*)malloc(sizeof(BST_Node));
    node->data = data;
    node->index = index;
    node->left = NULL;
    node->right = NULL;
    return node;
}

BST_Node* insert_bst(BST_Node *root, int data, int index, long long *comparisons) {
    if (root == NULL) {
        return create_node(data, index);
    }

    (*comparisons)++;
    if (data < root->data) {
        root->left = insert_bst(root->left, data, index, comparisons);
    } else if (data > root->data) {
        (*comparisons)++;
        root->right = insert_bst(root->right, data, index, comparisons);
    }

    return root;
}

int search_bst(BST_Node *root, int key, long long *comparisons) {
    *comparisons = 0;
    BST_Node *current = root;

    while (current != NULL) {
        (*comparisons)++;
        if (current->data == key) {
            return current->index;
        } else if (key < current->data) {
            (*comparisons)++;
            current = current->left;
        } else {
            (*comparisons)++;
            current = current->right;
        }
    }
    return -1;
}

void free_bst(BST_Node *root) {
    if (root != NULL) {
        free_bst(root->left);
        free_bst(root->right);
        free(root);
    }
}

// 4. 哈希表实现
#define HASH_SIZE 50000

typedef struct Hash_Node {
    int key;
    int index;
    struct Hash_Node *next;
} Hash_Node;

typedef struct {
    Hash_Node *table[HASH_SIZE];
} Hash_Table;

int hash_function(int key) {
    return key % HASH_SIZE;
}

Hash_Table* create_hash_table() {
    Hash_Table *ht = (Hash_Table*)malloc(sizeof(Hash_Table));
    for (int i = 0; i < HASH_SIZE; i++) {
        ht->table[i] = NULL;
    }
    return ht;
}

void insert_hash(Hash_Table *ht, int key, int index) {
    int hash_index = hash_function(key);
    Hash_Node *new_node = (Hash_Node*)malloc(sizeof(Hash_Node));
    new_node->key = key;
    new_node->index = index;
    new_node->next = ht->table[hash_index];
    ht->table[hash_index] = new_node;
}

int search_hash(Hash_Table *ht, int key, long long *comparisons) {
    *comparisons = 0;
    int hash_index = hash_function(key);
    Hash_Node *current = ht->table[hash_index];

    while (current != NULL) {
        (*comparisons)++;
        if (current->key == key) {
            return current->index;
        }
        current = current->next;
    }
    return -1;
}

void free_hash_table(Hash_Table *ht) {
    for (int i = 0; i < HASH_SIZE; i++) {
        Hash_Node *current = ht->table[i];
        while (current != NULL) {
            Hash_Node *temp = current;
            current = current->next;
            free(temp);
        }
    }
    free(ht);
}

// 测试排序算法的函数
void test_sorting_algorithm(const char *name, void (*sort_func)(int*, int), int *original_data) {
    int *data = (int*)malloc(DATA_SIZE * sizeof(int));
    copy_array(original_data, data, DATA_SIZE);

    printf("\n=== %s ===\n", name);
    clock_t start = clock();
    sort_func(data, DATA_SIZE);
    clock_t end = clock();

    double time_taken = ((double)(end - start)) / CLOCKS_PER_SEC;
    printf("比较次数: %lld\n", comparison_count);
    printf("运行时间: %.6f 秒\n", time_taken);

    // 验证排序是否正确
    bool is_sorted = true;
    for (int i = 0; i < DATA_SIZE - 1; i++) {
        if (data[i] > data[i + 1]) {
            is_sorted = false;
            break;
        }
    }
    printf("排序结果: %s\n", is_sorted ? "正确" : "错误");

    if (strcmp(name, "归并排序") == 0) {
        // 保存归并排序的结果用于后续查找测试
        copy_array(data, original_data, DATA_SIZE);
    }

    free(data);
}

// 测试查找算法的函数
void test_search_algorithms(int *sorted_data) {
    int target_value = sorted_data[TARGET_INDEX];
    long long comparisons;
    int result;
    clock_t start, end;
    double time_taken;

    printf("\n=== 查找算法测试 ===\n");
    printf("目标值: %d (位置: %d)\n", target_value, TARGET_INDEX);

    // 1. 顺序查找
    printf("\n--- 顺序查找 ---\n");
    start = clock();
    result = sequential_search(sorted_data, DATA_SIZE, target_value, &comparisons);
    end = clock();
    time_taken = ((double)(end - start)) / CLOCKS_PER_SEC;
    printf("找到位置: %d\n", result);
    printf("比较次数: %lld\n", comparisons);
    printf("运行时间: %.6f 秒\n", time_taken);

    // 2. 折半查找
    printf("\n--- 折半查找 ---\n");
    start = clock();
    result = binary_search(sorted_data, DATA_SIZE, target_value, &comparisons);
    end = clock();
    time_taken = ((double)(end - start)) / CLOCKS_PER_SEC;
    printf("找到位置: %d\n", result);
    printf("比较次数: %lld\n", comparisons);
    printf("运行时间: %.6f 秒\n", time_taken);

    // 3. 二叉搜索树查找
    printf("\n--- 二叉搜索树查找 ---\n");
    BST_Node *root = NULL;
    long long build_comparisons = 0;

    start = clock();
    for (int i = 0; i < DATA_SIZE; i++) {
        root = insert_bst(root, sorted_data[i], i, &build_comparisons);
    }

    result = search_bst(root, target_value, &comparisons);
    end = clock();
    time_taken = ((double)(end - start)) / CLOCKS_PER_SEC;

    printf("找到位置: %d\n", result);
    printf("构建BST比较次数: %lld\n", build_comparisons);
    printf("查找比较次数: %lld\n", comparisons);
    printf("总运行时间: %.6f 秒\n", time_taken);

    free_bst(root);

    // 4. 哈希表查找
    printf("\n--- 哈希表查找 ---\n");
    Hash_Table *ht = create_hash_table();

    start = clock();
    for (int i = 0; i < DATA_SIZE; i++) {
        insert_hash(ht, sorted_data[i], i);
    }

    result = search_hash(ht, target_value, &comparisons);
    end = clock();
    time_taken = ((double)(end - start)) / CLOCKS_PER_SEC;

    printf("找到位置: %d\n", result);
    printf("比较次数: %lld\n", comparisons);
    printf("总运行时间: %.6f 秒\n", time_taken);

    free_hash_table(ht);
}

int main() {
    printf("=== 排序与查找算法综合实验 ===\n");
    printf("数据规模: %d\n", DATA_SIZE);
    printf("数据范围: 0 ~ %d\n", MAX_VALUE);
    printf("目标查找位置: 第%d个数据 (下标%d)\n", TARGET_INDEX + 1, TARGET_INDEX);

    // 生成随机数据
    printf("\n正在生成%d个不重复的随机数...\n", DATA_SIZE);
    int *original_data = (int*)malloc(DATA_SIZE * sizeof(int));
    generate_unique_random_numbers(original_data, DATA_SIZE, MAX_VALUE);
    printf("随机数生成完成！\n");

    print_array_sample(original_data, DATA_SIZE, "原始随机数据");

    // 测试各种排序算法
    printf("\n\n=== 排序算法性能测试 ===\n");

    test_sorting_algorithm("冒泡排序", bubble_sort, original_data);
    test_sorting_algorithm("选择排序", selection_sort, original_data);
    test_sorting_algorithm("插入排序", insertion_sort, original_data);
    test_sorting_algorithm("希尔排序", shell_sort, original_data);
    test_sorting_algorithm("归并排序", merge_sort, original_data);
    test_sorting_algorithm("快速排序", quick_sort, original_data);
    test_sorting_algorithm("堆排序", heap_sort, original_data);
    test_sorting_algorithm("基数排序", radix_sort, original_data);

    // 使用归并排序的结果进行查找测试（因为它是稳定的）
    int *sorted_data = (int*)malloc(DATA_SIZE * sizeof(int));
    copy_array(original_data, sorted_data, DATA_SIZE);
    merge_sort(sorted_data, DATA_SIZE);

    print_array_sample(sorted_data, DATA_SIZE, "排序后数据");

    // 测试查找算法
    test_search_algorithms(sorted_data);

    // 输出硬件配置信息
    printf("\n=== 测试环境信息 ===\n");
    printf("注意: 请手动查看您的硬件配置\n");
    printf("- CPU物理核心数: [请查看系统信息]\n");
    printf("- CPU频率: [请查看系统信息]\n");
    printf("- 内存容量: [请查看系统信息]\n");

    free(original_data);
    free(sorted_data);

    return 0;
}
