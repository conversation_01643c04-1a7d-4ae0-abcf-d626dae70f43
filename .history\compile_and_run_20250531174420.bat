@echo off
echo ========================================
echo 排序与查找算法综合实验编译脚本
echo ========================================

echo.
echo 选择要编译的版本:
echo 1. 完整版 (sorting_searching_experiment.c) - 包含所有算法
echo 2. 快速版 (sorting_experiment_fast.c) - 快速测试版本
echo.
set /p choice="请输入选择 (1 或 2, 默认为 2): "

if "%choice%"=="" set choice=2
if "%choice%"=="1" (
    set SOURCE_FILE=sorting_searching_experiment.c
    set OUTPUT_FILE=sorting_experiment_full.exe
    echo 选择了完整版
) else (
    set SOURCE_FILE=sorting_experiment_fast.c
    set OUTPUT_FILE=sorting_experiment_fast.exe
    echo 选择了快速版
)

echo.
echo 正在检查编译器...

:: 检查是否有GCC
where gcc >nul 2>&1
if %errorlevel% == 0 (
    echo 找到GCC编译器，使用GCC编译...
    gcc -Wall -Wextra -std=c99 -O2 -o sorting_experiment.exe sorting_searching_experiment.c
    if %errorlevel% == 0 (
        echo 编译成功！
        echo.
        echo 开始运行程序...
        echo ========================================
        sorting_experiment.exe
        goto :end
    ) else (
        echo GCC编译失败！
        goto :try_msvc
    )
)

:try_msvc
:: 检查是否有MSVC
where cl >nul 2>&1
if %errorlevel% == 0 (
    echo 找到MSVC编译器，使用MSVC编译...
    cl /W4 /O2 sorting_searching_experiment.c /Fe:sorting_experiment.exe
    if %errorlevel% == 0 (
        echo 编译成功！
        echo.
        echo 开始运行程序...
        echo ========================================
        sorting_experiment.exe
        goto :end
    ) else (
        echo MSVC编译失败！
        goto :no_compiler
    )
)

:no_compiler
echo.
echo 错误：未找到可用的C编译器！
echo.
echo 请安装以下编译器之一：
echo 1. MinGW-w64 (推荐)
echo    下载地址: https://www.mingw-w64.org/
echo.
echo 2. Microsoft Visual Studio
echo    下载地址: https://visualstudio.microsoft.com/
echo.
echo 3. 或者使用在线编译器测试代码
echo.
goto :end

:end
echo.
echo ========================================
echo 程序执行完毕
echo ========================================
pause
